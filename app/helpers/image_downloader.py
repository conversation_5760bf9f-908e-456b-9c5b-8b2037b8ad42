import io
import requests
from PIL import Image
from typing import <PERSON><PERSON>
import numpy as np

from app.logging import logger


def download_image_to_memory(url: str, timeout: int = 30, max_size_mb: int = 10) -> Tuple[np.ndarray | None, str | None]:
    """
    Download an image from URL and load it into memory as numpy array.
    
    Args:
        url: The image URL to download
        timeout: Request timeout in seconds
        max_size_mb: Maximum file size in MB
        
    Returns:
        Tuple of (image_array, error_message)
        image_array is None if there was an error
    """
    try:
        logger.info(f"Downloading image from: {url}")
        
        # Download the image
        response = requests.get(url, timeout=timeout, stream=True)
        response.raise_for_status()
        
        # Check content length if available
        content_length = response.headers.get('content-length')
        if content_length:
            size_mb = int(content_length) / (1024 * 1024)
            if size_mb > max_size_mb:
                return None, f"Image too large: {size_mb:.1f}MB (max: {max_size_mb}MB)"
        
        # Read image data into memory
        image_data = io.BytesIO()
        downloaded_size = 0
        max_size_bytes = max_size_mb * 1024 * 1024
        
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                downloaded_size += len(chunk)
                if downloaded_size > max_size_bytes:
                    return None, f"Image too large: exceeded {max_size_mb}MB during download"
                image_data.write(chunk)
        
        image_data.seek(0)
        
        # Load image using PIL
        try:
            pil_image = Image.open(image_data)
            # Convert to RGB if necessary (handles RGBA, grayscale, etc.)
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # Convert PIL image to numpy array (RGB format)
            image_array = np.array(pil_image)
            
            logger.info(f"Successfully loaded image: {image_array.shape}")
            return image_array, None
            
        except Exception as e:
            return None, f"Failed to load image: {str(e)}"
        
    except requests.exceptions.Timeout:
        return None, "Download timeout"
    except requests.exceptions.ConnectionError:
        return None, "Connection error during download"
    except requests.exceptions.HTTPError as e:
        return None, f"HTTP error: {e.response.status_code}"
    except requests.exceptions.RequestException as e:
        return None, f"Download error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error downloading image from {url}: {str(e)}")
        return None, f"Download error: {str(e)}"
