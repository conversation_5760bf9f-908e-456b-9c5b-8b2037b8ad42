import os
from typing import List, <PERSON>ple

import insightface
import numpy as np

from app.logging import logger


class FaceDetector:
    """Face detector using InsightFace for CPU-only detection."""

    def __init__(self):
        """Initialize the face detector with InsightFace model."""
        try:
            # Initialize InsightFace app with CPU providers only
            self.app = insightface.app.FaceAnalysis(
                providers=["CPUExecutionProvider"],  # Force CPU-only processing
                allowed_modules=[
                    "detection"
                ],  # Only load detection module for efficiency
            )

            # Prepare the model (downloads models if needed)
            self.app.prepare(ctx_id=0, det_size=(640, 640))

            logger.info(
                "InsightFace detector initialized successfully with CPU providers"
            )

        except Exception as e:
            error_msg = f"Failed to initialize InsightFace detector: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

    def detect_faces(
        self, image: np.ndarray
    ) -> Tuple[bool, List[Tuple[int, int, int, int]], str | None]:
        """
        Detect faces in an image using InsightFace.

        Args:
            image: Input image as numpy array (RGB format)

        Returns:
            Tuple of (has_faces, face_rectangles, error_message)
            face_rectangles is a list of (x, y, width, height) tuples
        """
        try:
            if image is None or image.size == 0:
                return False, [], "Invalid or empty image"

            # Validate image format
            if len(image.shape) != 3 or image.shape[2] != 3:
                return False, [], "Image must be in RGB format with 3 channels"

            # InsightFace expects BGR format, so convert RGB to BGR
            image_bgr = image[:, :, ::-1]  # RGB to BGR conversion

            # Detect faces using InsightFace
            faces = self.app.get(image_bgr)

            # Convert InsightFace face objects to bounding box tuples
            face_rectangles = []
            for face in faces:
                # InsightFace returns bbox as [x1, y1, x2, y2]
                bbox = face.bbox.astype(int)
                x, y, x2, y2 = bbox
                w = x2 - x
                h = y2 - y
                face_rectangles.append((x, y, w, h))

            has_faces = len(face_rectangles) > 0

            if has_faces:
                logger.info(f"Detected {len(face_rectangles)} face(s) in image")
            else:
                logger.info("No faces detected in image")

            return has_faces, face_rectangles, None

        except Exception as e:
            error_msg = f"Face detection error: {str(e)}"
            logger.error(error_msg)
            return False, [], error_msg


def detect_faces_in_image(image: np.ndarray) -> Tuple[bool, str | None]:
    """
    Convenience function to detect if an image contains faces.

    Args:
        image: Input image as numpy array (RGB format)

    Returns:
        Tuple of (has_faces, error_message)
    """
    try:
        detector = FaceDetector()
        has_faces, _, error = detector.detect_faces(image)
        return has_faces, error
    except Exception as e:
        error_msg = f"Failed to initialize face detector: {str(e)}"
        logger.error(error_msg)
        return False, error_msg
