from fastapi import APIRouter, HTTPException
from pydantic import HttpUrl

from app.logging import logger
from app.models import FaceValidationResponse
from app.services import face_validation_service

face_router = APIRouter(prefix="/face")


@face_router.get("/validate", response_model=FaceValidationResponse)
async def validate_face(fileId: str, fileUrl: HttpUrl) -> FaceValidationResponse:
    """
    Validate if an image from a URL contains faces.

    Args:
        fileId: ID of the file (for logging purposes, currently ignored)
        fileUrl: URL of the image to validate

    Returns:
        FaceValidationResponse with has_face boolean and optional message/error
    """
    logger.info(f"Received face validation request for file {fileId} from {fileUrl}")

    try:
        # Convert HttpUrl to string for processing
        url_str = str(fileUrl)

        # Use the face validation service
        result = await face_validation_service.validate_face_from_url(url_str, fileId)

        logger.info(
            f"Face validation completed for {fileId}: has_face={result.has_face}"
        )
        return result

    except Exception as e:
        error_msg = (
            f"Unexpected error in face validation endpoint for {fileId}: {str(e)}"
        )
        logger.error(error_msg)
        return FaceValidationResponse(has_face=False, error=error_msg)
