from datetime import datetime

from fastapi import APIRouter
from fastapi.exceptions import HTTPException
from fastapi.responses import JSONResponse

from app.api.face_routes import face_router
from app.config import BUILD_DATE, ENVIRONMENT, TITLE, VERSION
from app.logging import logger

UP_TIME = datetime.now()

router = APIRouter()


@router.get("/", include_in_schema=False)
async def root() -> JSONResponse:
    build_time = datetime.fromtimestamp(int(BUILD_DATE))
    build_time_str = build_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    return JSONResponse(
        {
            "build_time": build_time_str,
            "environment": ENVIRONMENT,
            "message": f"Welcome to {TITLE}!",
            "version": f"{VERSION}.{BUILD_DATE}",
            "uptime": f"{(datetime.now() - UP_TIME).total_seconds():.2f} seconds",
        }
    )


routers = [router, face_router]
