import os

from dotenv import find_dotenv, load_dotenv

try:
    load_dotenv(find_dotenv(".env"))
    load_dotenv(find_dotenv("thisversion.env"))
except Exception as e:
    pass

# SERIVCE CONFIG
TITLE = "GenAI Service"
DESCRIPTION = "API for genai functionality."

ROOT_PATH = os.getenv("ROOT_PATH", "")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

VERSION = os.getenv("VERSION", "0.0.0")
BUILD_DATE = os.getenv("TIMESTAMP", "0")
ENVIRONMENT = os.getenv("ENVIRONMENT", "development").lower()


# API CONFIG
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", 8000))
DEBUG_MODULES = os.getenv(
    "DEBUG_MODULES", "app.api,app.services,watchfiles.main"
).split(",")
