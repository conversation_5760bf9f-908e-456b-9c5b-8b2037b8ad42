2025-05-29 15:43:35.812 | INFO     | uvicorn.server:_serve:83 - Started server process [341136]
2025-05-29 15:43:35.813 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:43:35.813 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:43:38.909 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:52676 - "GET / HTTP/1.1" 200
2025-05-29 15:43:38.982 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:52676 - "GET /favicon.ico HTTP/1.1" 404
2025-05-29 15:43:45.018 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:44618 - "GET / HTTP/1.1" 200
2025-05-29 15:43:51.697 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:44624 - "GET /docs HTTP/1.1" 200
2025-05-29 15:43:52.144 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:44624 - "GET /openapi.json HTTP/1.1" 200
2025-05-29 15:44:17.579 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:44:17.680 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:44:17.680 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:44:17.680 | INFO     | uvicorn.server:_serve:93 - Finished server process [341136]
2025-05-29 15:44:17.927 | INFO     | uvicorn.server:_serve:83 - Started server process [341745]
2025-05-29 15:44:17.928 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:44:17.928 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:44:19.093 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:46464 - "GET /docs HTTP/1.1" 200
2025-05-29 15:44:19.144 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:46464 - "GET /openapi.json HTTP/1.1" 200
2025-05-29 15:44:26.241 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:45022 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=sdfsdfsdf HTTP/1.1" 422
2025-05-29 15:44:34.082 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:57656 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=www.google.com HTTP/1.1" 422
2025-05-29 15:44:38.261 | INFO     | app.api.face_routes:validate_face:12 - Validating face for file sdfsdfsd from http://www.google.com/
2025-05-29 15:44:38.262 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:57656 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=http%3A%2F%2Fwww.google.com HTTP/1.1" 200
2025-05-29 15:44:40.788 | INFO     | app.api.face_routes:validate_face:12 - Validating face for file sdfsdfsd from https://www.google.com/
2025-05-29 15:44:40.788 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:57656 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fwww.google.com HTTP/1.1" 200
2025-05-29 15:51:05.233 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:51:05.335 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:51:05.336 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:51:05.336 | INFO     | uvicorn.server:_serve:93 - Finished server process [341745]
2025-05-29 15:51:05.601 | INFO     | uvicorn.server:_serve:83 - Started server process [345756]
2025-05-29 15:51:05.601 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:51:05.601 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:51:58.434 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:51:58.537 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:51:58.537 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:51:58.537 | INFO     | uvicorn.server:_serve:93 - Finished server process [345756]
2025-05-29 15:51:58.737 | INFO     | uvicorn.server:_serve:83 - Started server process [346300]
2025-05-29 15:51:58.738 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:51:58.738 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:52:19.308 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:52:19.410 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:52:19.411 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:52:19.411 | INFO     | uvicorn.server:_serve:93 - Finished server process [346300]
2025-05-29 15:52:19.698 | INFO     | uvicorn.server:_serve:83 - Started server process [346556]
2025-05-29 15:52:19.698 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:52:19.699 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:52:40.382 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:52:40.483 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:52:40.483 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:52:40.483 | INFO     | uvicorn.server:_serve:93 - Finished server process [346556]
2025-05-29 15:52:41.106 | INFO     | uvicorn.server:_serve:83 - Started server process [346809]
2025-05-29 15:52:41.106 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:52:41.106 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:54:22.675 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:54:22.777 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:54:22.777 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:54:22.777 | INFO     | uvicorn.server:_serve:93 - Finished server process [346809]
2025-05-29 15:54:37.623 | INFO     | uvicorn.server:_serve:83 - Started server process [348260]
2025-05-29 15:54:37.623 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:54:37.623 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:55:01.149 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:60572 - "GET /docs HTTP/1.1" 200
2025-05-29 15:55:01.618 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:60572 - "GET /openapi.json HTTP/1.1" 200
2025-05-29 15:55:19.458 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file test123 from https://upload.wikimedia.org/wikipedia/commons/thumb/5/50/Vd-Orig.png/256px-Vd-Orig.png
2025-05-29 15:55:19.458 | INFO     | app.services.face_validation_service:validate_face_from_url:26 - Starting face validation for file test123 from URL: https://upload.wikimedia.org/wikipedia/commons/thumb/5/50/Vd-Orig.png/256px-Vd-Orig.png
2025-05-29 15:55:19.697 | WARNING  | app.services.face_validation_service:validate_face_from_url:32 - URL validation failed for test123: HTTP 403: Unable to access URL
2025-05-29 15:55:19.698 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for test123: has_face=False
2025-05-29 15:55:19.698 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:52716 - "GET /face/validate?fileId=test123&fileUrl=https%3A%2F%2Fupload.wikimedia.org%2Fwikipedia%2Fcommons%2Fthumb%2F5%2F50%2FVd-Orig.png%2F256px-Vd-Orig.png HTTP/1.1" 200
2025-05-29 15:56:33.407 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file test123 from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 15:56:33.407 | INFO     | app.services.face_validation_service:validate_face_from_url:26 - Starting face validation for file test123 from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 15:56:35.123 | INFO     | app.services.face_validation_service:validate_face_from_url:38 - URL validation successful for test123
2025-05-29 15:56:35.123 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 15:56:36.416 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 15:56:36.417 | INFO     | app.services.face_validation_service:validate_face_from_url:49 - Image download successful for test123
2025-05-29 15:56:36.428 | INFO     | app.helpers.face_detector:__init__:25 - Face detector initialized successfully
2025-05-29 15:56:36.702 | INFO     | app.helpers.face_detector:detect_faces:66 - Detected 3 face(s) in image
2025-05-29 15:56:36.702 | INFO     | app.services.face_validation_service:validate_face_from_url:62 - Face validation completed for test123: faces detected
2025-05-29 15:56:36.703 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for test123: has_face=True
2025-05-29 15:56:36.703 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:35508 - "GET /face/validate?fileId=test123&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 15:56:54.531 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdf from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 15:56:54.531 | INFO     | app.services.face_validation_service:validate_face_from_url:26 - Starting face validation for file sdf from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 15:56:54.643 | INFO     | app.services.face_validation_service:validate_face_from_url:38 - URL validation successful for sdf
2025-05-29 15:56:54.643 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 15:56:55.053 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 15:56:55.053 | INFO     | app.services.face_validation_service:validate_face_from_url:49 - Image download successful for sdf
2025-05-29 15:56:55.061 | INFO     | app.helpers.face_detector:__init__:25 - Face detector initialized successfully
2025-05-29 15:56:55.327 | INFO     | app.helpers.face_detector:detect_faces:66 - Detected 3 face(s) in image
2025-05-29 15:56:55.327 | INFO     | app.services.face_validation_service:validate_face_from_url:62 - Face validation completed for sdf: faces detected
2025-05-29 15:56:55.330 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdf: has_face=True
2025-05-29 15:56:55.330 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:60584 - "GET /face/validate?fileId=sdf&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 15:57:22.622 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:57:22.724 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:57:22.725 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:57:22.725 | INFO     | uvicorn.server:_serve:93 - Finished server process [348260]
2025-05-29 15:57:23.035 | INFO     | uvicorn.server:_serve:83 - Started server process [350166]
2025-05-29 15:57:23.036 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:57:23.036 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:57:42.262 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:57:42.363 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:57:42.364 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:57:42.364 | INFO     | uvicorn.server:_serve:93 - Finished server process [350166]
2025-05-29 15:57:42.667 | INFO     | uvicorn.server:_serve:83 - Started server process [350412]
2025-05-29 15:57:42.668 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:57:42.668 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:57:45.550 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:57:45.651 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:57:45.651 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:57:45.651 | INFO     | uvicorn.server:_serve:93 - Finished server process [350412]
2025-05-29 15:57:45.939 | INFO     | uvicorn.server:_serve:83 - Started server process [350480]
2025-05-29 15:57:45.939 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:57:45.940 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 15:57:53.078 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 15:57:53.179 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 15:57:53.179 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 15:57:53.179 | INFO     | uvicorn.server:_serve:93 - Finished server process [350480]
2025-05-29 15:57:53.480 | INFO     | uvicorn.server:_serve:83 - Started server process [350595]
2025-05-29 15:57:53.480 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 15:57:53.480 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:02:43.661 | INFO     | uvicorn.server:_serve:83 - Started server process [354087]
2025-05-29 16:02:43.661 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:02:43.661 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:03:02.526 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:02.526 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:02.644 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:03:02.644 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:02.849 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 16:03:02.850 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:03:03.798 | INFO     | app.helpers.face_detector:__init__:26 - InsightFace detector initialized successfully with CPU providers
2025-05-29 16:03:03.967 | INFO     | app.helpers.face_detector:detect_faces:75 - Detected 1 face(s) in image
2025-05-29 16:03:03.986 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:03:03.986 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:03:03.986 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:46552 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:03:16.591 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:16.591 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:16.716 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:03:16.716 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:17.131 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 16:03:17.131 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:03:18.067 | INFO     | app.helpers.face_detector:__init__:26 - InsightFace detector initialized successfully with CPU providers
2025-05-29 16:03:18.171 | INFO     | app.helpers.face_detector:detect_faces:75 - Detected 1 face(s) in image
2025-05-29 16:03:18.173 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:03:18.174 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:03:18.174 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:60526 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:03:18.413 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:18.413 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:18.534 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:03:18.535 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:03:18.985 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 16:03:18.985 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:03:19.975 | INFO     | app.helpers.face_detector:__init__:26 - InsightFace detector initialized successfully with CPU providers
2025-05-29 16:03:20.081 | INFO     | app.helpers.face_detector:detect_faces:75 - Detected 1 face(s) in image
2025-05-29 16:03:20.100 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:03:20.100 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:03:20.100 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:60526 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:03:31.317 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:03:31.317 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:03:31.533 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:03:31.533 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:03:31.673 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2702, 2674, 3)
2025-05-29 16:03:31.673 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:03:32.362 | INFO     | app.helpers.face_detector:__init__:26 - InsightFace detector initialized successfully with CPU providers
2025-05-29 16:03:32.490 | INFO     | app.helpers.face_detector:detect_faces:77 - No faces detected in image
2025-05-29 16:03:32.508 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:03:32.508 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:03:32.509 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:45180 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fcamo.githubusercontent.com%2F8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e%2F68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 HTTP/1.1" 200
2025-05-29 16:03:43.640 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:03:43.743 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:03:43.744 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:03:43.744 | INFO     | uvicorn.server:_serve:93 - Finished server process [354087]
2025-05-29 16:05:50.013 | INFO     | uvicorn.server:_serve:83 - Started server process [357049]
2025-05-29 16:05:50.013 | INFO     | uvicorn.lifespan.on:startup:48 - Waiting for application startup.
2025-05-29 16:05:50.013 | INFO     | uvicorn.lifespan.on:startup:62 - Application startup complete.
2025-05-29 16:05:51.787 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:51.788 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:51.833 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:05:51.834 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:51.982 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2702, 2674, 3)
2025-05-29 16:05:51.983 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:05:52.671 | INFO     | app.helpers.face_detector:__init__:26 - InsightFace detector initialized successfully with CPU providers
2025-05-29 16:05:51.364 | INFO     | app.helpers.face_detector:detect_faces:77 - No faces detected in image
2025-05-29 16:05:51.364 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:05:51.365 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:05:51.365 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:49046 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fcamo.githubusercontent.com%2F8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e%2F68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 HTTP/1.1" 200
2025-05-29 16:05:52.525 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:52.525 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:52.559 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:05:52.559 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:52.686 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2702, 2674, 3)
2025-05-29 16:05:52.687 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:05:52.801 | INFO     | app.helpers.face_detector:detect_faces:77 - No faces detected in image
2025-05-29 16:05:52.801 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:05:52.802 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:05:52.802 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:49046 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fcamo.githubusercontent.com%2F8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e%2F68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 HTTP/1.1" 200
2025-05-29 16:05:54.537 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:54.537 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:54.584 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:05:54.584 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:54.686 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2702, 2674, 3)
2025-05-29 16:05:54.687 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:05:54.853 | INFO     | app.helpers.face_detector:detect_faces:77 - No faces detected in image
2025-05-29 16:05:54.853 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:05:54.854 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:05:54.854 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:49046 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fcamo.githubusercontent.com%2F8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e%2F68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 HTTP/1.1" 200
2025-05-29 16:05:55.783 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:55.783 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:55.832 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:05:55.832 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067
2025-05-29 16:05:55.927 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2702, 2674, 3)
2025-05-29 16:05:55.927 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:05:56.041 | INFO     | app.helpers.face_detector:detect_faces:77 - No faces detected in image
2025-05-29 16:05:56.041 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: no faces detected
2025-05-29 16:05:56.041 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=False
2025-05-29 16:05:56.042 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:49046 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fcamo.githubusercontent.com%2F8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e%2F68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067 HTTP/1.1" 200
2025-05-29 16:06:01.064 | INFO     | app.api.face_routes:validate_face:23 - Received face validation request for file sdfsdfsd from https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:06:01.064 | INFO     | app.services.face_validation_service:validate_face_from_url:32 - Starting face validation for file sdfsdfsd from URL: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:06:01.099 | INFO     | app.services.face_validation_service:validate_face_from_url:43 - URL validation successful for sdfsdfsd
2025-05-29 16:06:01.100 | INFO     | app.helpers.image_downloader:download_image_to_memory:24 - Downloading image from: https://th.bing.com/th/id/R.********************************?rik=ysCyR3%2bNP08lDA&riu=http%3a%2f%2fi.huffpost.com%2fgen%2f4459084%2foriginal.jpg&ehk=KdYL%2bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%3d&risl=&pid=ImgRaw&r=0
2025-05-29 16:06:01.298 | INFO     | app.helpers.image_downloader:download_image_to_memory:61 - Successfully loaded image: (2124, 3014, 3)
2025-05-29 16:06:01.298 | INFO     | app.services.face_validation_service:validate_face_from_url:53 - Image download successful for sdfsdfsd
2025-05-29 16:06:01.481 | INFO     | app.helpers.face_detector:detect_faces:75 - Detected 1 face(s) in image
2025-05-29 16:06:01.481 | INFO     | app.services.face_validation_service:validate_face_from_url:65 - Face validation completed for sdfsdfsd: faces detected
2025-05-29 16:06:01.482 | INFO     | app.api.face_routes:validate_face:32 - Face validation completed for sdfsdfsd: has_face=True
2025-05-29 16:06:01.483 | INFO     | uvicorn.protocols.http.httptools_impl:send:476 - 127.0.0.1:55756 - "GET /face/validate?fileId=sdfsdfsd&fileUrl=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FR.********************************%3Frik%3DysCyR3%252bNP08lDA%26riu%3Dhttp%253a%252f%252fi.huffpost.com%252fgen%252f4459084%252foriginal.jpg%26ehk%3DKdYL%252bOcs1y5xC5tbCspW47E9IUieGx4Q6UXeqQL9KOI%253d%26risl%3D%26pid%3DImgRaw%26r%3D0 HTTP/1.1" 200
2025-05-29 16:06:10.208 | INFO     | uvicorn.server:shutdown:263 - Shutting down
2025-05-29 16:06:10.308 | INFO     | uvicorn.lifespan.on:shutdown:67 - Waiting for application shutdown.
2025-05-29 16:06:10.309 | INFO     | uvicorn.lifespan.on:shutdown:76 - Application shutdown complete.
2025-05-29 16:06:10.309 | INFO     | uvicorn.server:_serve:93 - Finished server process [357049]
