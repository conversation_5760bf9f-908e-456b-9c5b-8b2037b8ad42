from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.api import routers
from app.config import BUILD_DATE, DESCRIPTION, HOST, PORT, TITLE, VERSION
from app.logging import init_logger

init_logger()

app = FastAPI(
    title=TITLE,
    version=f"{VERSION}.{BUILD_DATE}",
    description=DESCRIPTION,
    swagger_ui_parameters={"defaultModelsExpandDepth": 0},
)

for router in routers:
    app.include_router(router)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host=HOST, port=PORT)
