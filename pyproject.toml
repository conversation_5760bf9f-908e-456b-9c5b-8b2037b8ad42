[project]
name = "face-validation"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi>=0.115.12",
    "httptools>=0.6.4",
    "httpx>=0.28.1",
    "insightface>=0.7.3",
    "loguru>=0.7.3",
    "opencv-python>=*********",
    "pillow>=11.2.1",
    "python-dotenv>=1.1.0",
    "requests>=2.32.3",
    "uvicorn>=0.34.2",
    "uvloop>=0.21.0",
]
